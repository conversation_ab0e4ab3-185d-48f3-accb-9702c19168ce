/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> AD9854 Signal Generator - Complete Control System
  * @version V2.0
  * @date    2024
  * @brief   STM32F4 + AD9854 + Dual Matrix Keypad + OLED Display + Control System
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// AD9854信号生成模块
#include "../Modules/Generation/ad9854.h"

// 用户界面模块
#include "../Modules/Interface/oled.h"
#include "../Modules/Interface/matrix_keypad.h"

// 控制系统模块
#include "../Modules/Control/signal_generator.h"
#include "../Modules/Control/ad9854_control.h"

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void Error_Handler(void);

/**
  * @brief  主函数 - 完整信号发生器控制系统
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* ==================== 电赛G题：AD9854信号发生器控制系统 ==================== */
    // 初始化信号发生器控制系统 (包含AD9854、OLED、矩阵键盘)
    if (SignalGenerator_Init() != 0) {
        // 初始化失败，进入错误处理
        Error_Handler();
    }

    // 等待系统稳定
    Delay_ms(100);

    /* ==================== 信号发生器控制系统就绪！==================== */
    // 功能特性：
    // - AD9854 DDS信号生成 (1Hz - 50MHz)
    // - 双4x4矩阵键盘控制
    // - OLED实时显示
    // - 频率/幅度/波形控制
    // - 默认输出：5MHz正弦波，0.5V峰峰值

    /* 主循环 - 信号发生器控制系统 */
    while (1)
    {
        // 处理信号发生器逻辑 (键盘输入、显示更新、参数控制)
        SignalGenerator_Process();

        // 短暂延时，避免过度占用CPU
        Delay_ms(10);
    }
}








/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}



/**
 * @brief  错误处理函数
 * @param  None
 * @retval None
 */
static void Error_Handler(void)
{
    // 错误指示：LED快速闪烁
    while (1) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(50);  // 快速闪烁表示错误
    }
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif




