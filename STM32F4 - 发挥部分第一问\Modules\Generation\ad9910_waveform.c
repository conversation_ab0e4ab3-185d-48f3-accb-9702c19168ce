/**
  ******************************************************************************
  * @file    ad9910_waveform.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   AD9910波形控制层实现文件 - 高级功能接口
  ******************************************************************************
  * @attention
  *
  * 本文件实现AD9910 DDS芯片的高级波形控制功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "ad9910_waveform.h"
#include "ad9910_hal.h"      // 包含AD9910_SYSTEM_CLOCK定义
#include <stddef.h>  // 包含NULL定义
#include <stdbool.h> // 包含bool定义

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/* AD9910寄存器配置数据 (高质量优化版本) */
static const uint8_t cfr1_data[] = {0x00, 0x40, 0x08, 0x20};  ///< CFR1配置 (启用反SINC滤波器+相位连续)
static const uint8_t cfr2_data[] = {0x01, 0x08, 0x09, 0x00};  ///< CFR2配置 (启用幅度比例因子+同步时钟)
static const uint8_t cfr3_data[] = {0x05, 0x0F, 0x41, 0x32};  ///< CFR3配置 (1GHz PLL, VCO=5, ICP=1)

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static AD9910_Config_t current_config;  ///< 当前配置
static uint8_t profile_data[8] = {0x3f, 0xff, 0x00, 0x00, 0x25, 0x09, 0x7b, 0x42}; ///< Profile数据

/* 高精度控制变量 */
static double frequency_calibration_factor = 1.0;  ///< 频率校准因子
static double amplitude_calibration_factor = 1.0;  ///< 幅度校准因子
static int16_t temperature_offset = 0;              ///< 温度补偿偏移

/* Private function prototypes -----------------------------------------------*/
static void AD9910_WriteProfile(void);
static uint32_t AD9910_CalculateFrequencyWord(uint32_t frequency_hz);
static uint16_t AD9910_CalculateAmplitudeWord(uint16_t amplitude_mv);
static uint32_t AD9910_CalculateFrequencyWord_Precision(uint32_t frequency_hz);
static uint16_t AD9910_CalculateAmplitudeWord_Precision(uint16_t amplitude_mv);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  AD9910初始化
 * @param  None
 * @retval None
 */
void AD9910_Init(void)
{
    uint8_t i;

    /* 硬件初始化 */
    AD9910_HAL_Init();
    
    /* 电源控制 */
    AD9910_HAL_PowerControl(1);  // 使能电源
    
    /* Profile选择 */
    AD9910_HAL_SelectProfile(0); // 选择Profile 0
    
    /* 硬件复位 */
    AD9910_HAL_Reset();
    
    /* 写入CFR1寄存器 */
    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(AD9910_REG_CFR1);
    for (i = 0; i < 4; i++) {
        AD9910_HAL_Send8Bits(cfr1_data[i]);
    }
    AD9910_CSB_HIGH();
    AD9910_HAL_DelayUs(10);
    
    /* 写入CFR2寄存器 */
    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(AD9910_REG_CFR2);
    for (i = 0; i < 4; i++) {
        AD9910_HAL_Send8Bits(cfr2_data[i]);
    }
    AD9910_CSB_HIGH();
    AD9910_HAL_DelayUs(10);
    
    /* 写入CFR3寄存器 */
    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(AD9910_REG_CFR3);
    for (i = 0; i < 4; i++) {
        AD9910_HAL_Send8Bits(cfr3_data[i]);
    }
    AD9910_CSB_HIGH();
    AD9910_HAL_DelayUs(10);
    
    /* I/O更新 */
    AD9910_HAL_IOUpdate();
    AD9910_HAL_DelayMs(1);

    /* 设置默认配置 */
    current_config.frequency_hz = AD9910_DEFAULT_FREQUENCY;
    current_config.amplitude_mv = AD9910_DEFAULT_AMPLITUDE;
    current_config.phase_deg = AD9910_DEFAULT_PHASE;
    current_config.wave_type = AD9910_WAVE_SINE;
    current_config.profile = AD9910_DEFAULT_PROFILE;

    /* 立即设置基准测试信号：1MHz，0.5V */
    AD9910_SetFrequency(1000000);  // 1MHz
    AD9910_SetAmplitude(500);      // 0.5V峰峰值

    /* 确保输出使能 */
    AD9910_EnableOutput();

    /* 立即设置基准测试信号：1MHz，0.5V */
    AD9910_SetFrequency(1000000);  // 1MHz
    AD9910_SetAmplitude(500);      // 0.5V峰峰值

    /* 确保输出使能 */
    AD9910_EnableOutput();
}

/**
 * @brief  设置AD9910输出频率
 * @param  frequency_hz: 频率值 (1Hz - 420MHz)
 * @retval None
 */
void AD9910_SetFrequency(uint32_t frequency_hz)
{
    uint32_t freq_word;
    
    /* 参数检查 */
    if (frequency_hz < AD9910_MIN_FREQ_HZ) {
        frequency_hz = AD9910_MIN_FREQ_HZ;
    }
    if (frequency_hz > AD9910_MAX_FREQ_HZ) {
        frequency_hz = AD9910_MAX_FREQ_HZ;
    }
    
    /* 计算频率控制字 */
    freq_word = AD9910_CalculateFrequencyWord(frequency_hz);
    
    /* 更新Profile数据 */
    profile_data[7] = (uint8_t)(freq_word & 0xFF);
    profile_data[6] = (uint8_t)((freq_word >> 8) & 0xFF);
    profile_data[5] = (uint8_t)((freq_word >> 16) & 0xFF);
    profile_data[4] = (uint8_t)((freq_word >> 24) & 0xFF);
    
    /* 写入Profile寄存器 */
    AD9910_WriteProfile();
    
    /* 更新当前配置 */
    current_config.frequency_hz = frequency_hz;
}

/**
 * @brief  设置AD9910输出幅度
 * @param  amplitude_mv: 幅度值 (0 - 800mV峰峰值)
 * @retval None
 */
void AD9910_SetAmplitude(uint16_t amplitude_mv)
{
    uint16_t amp_word;
    
    /* 参数检查 */
    if (amplitude_mv > AD9910_MAX_AMPLITUDE_MV) {
        amplitude_mv = AD9910_MAX_AMPLITUDE_MV;
    }
    
    /* 计算幅度控制字 */
    amp_word = AD9910_CalculateAmplitudeWord(amplitude_mv);
    
    /* 更新Profile数据 */
    profile_data[0] = (uint8_t)((amp_word >> 8) & 0x3F);  // 高6位
    profile_data[1] = (uint8_t)(amp_word & 0xFF);         // 低8位
    
    /* 写入Profile寄存器 */
    AD9910_WriteProfile();
    
    /* 更新当前配置 */
    current_config.amplitude_mv = amplitude_mv;
}

/**
 * @brief  设置AD9910输出相位
 * @param  phase_deg: 相位值 (0 - 359度)
 * @retval None
 */
void AD9910_SetPhase(uint16_t phase_deg)
{
    uint16_t phase_word;
    
    /* 参数检查 */
    if (phase_deg >= 360) {
        phase_deg = phase_deg % 360;
    }
    
    /* 计算相位控制字 (16位) */
    phase_word = (uint16_t)((uint32_t)phase_deg * 65536 / 360);
    
    /* 更新Profile数据 */
    profile_data[2] = (uint8_t)((phase_word >> 8) & 0xFF);
    profile_data[3] = (uint8_t)(phase_word & 0xFF);
    
    /* 写入Profile寄存器 */
    AD9910_WriteProfile();
    
    /* 更新当前配置 */
    current_config.phase_deg = phase_deg;
}

/**
 * @brief  设置AD9910波形类型
 * @param  wave_type: 波形类型
 * @retval None
 */
void AD9910_SetWaveType(AD9910_WaveType_t wave_type)
{
    /* 更新当前配置 */
    current_config.wave_type = wave_type;
    
    /* 注意：AD9910主要输出正弦波，其他波形需要特殊配置 */
    /* 这里保持正弦波输出，实际项目中可根据需要扩展 */
}

/**
 * @brief  使能AD9910输出
 * @param  None
 * @retval None
 */
void AD9910_EnableOutput(void)
{
    /* 通过OSK引脚控制输出使能 */
    AD9910_OSK_LOW();  // 低电平使能输出
}

/**
 * @brief  禁用AD9910输出
 * @param  None
 * @retval None
 */
void AD9910_DisableOutput(void)
{
    /* 通过OSK引脚控制输出禁用 */
    AD9910_OSK_HIGH(); // 高电平禁用输出
}

/**
 * @brief  配置AD9910完整参数
 * @param  config: 配置结构体指针
 * @retval None
 */
void AD9910_Configure(const AD9910_Config_t* config)
{
    if (config == NULL) return;
    
    AD9910_SetFrequency(config->frequency_hz);
    AD9910_SetAmplitude(config->amplitude_mv);
    AD9910_SetPhase(config->phase_deg);
    AD9910_SetWaveType(config->wave_type);
    AD9910_HAL_SelectProfile(config->profile);
}

/**
 * @brief  写入Profile寄存器
 * @param  None
 * @retval None
 */
static void AD9910_WriteProfile(void)
{
    uint8_t i;
    
    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(AD9910_REG_PROFILE0);  // 写入Profile 0
    for (i = 0; i < 8; i++) {
        AD9910_HAL_Send8Bits(profile_data[i]);
    }
    AD9910_CSB_HIGH();
    
    /* I/O更新 */
    AD9910_HAL_IOUpdate();
}

/**
 * @brief  计算频率控制字 (标准版本)
 * @param  frequency_hz: 频率值
 * @retval 频率控制字
 */
static uint32_t AD9910_CalculateFrequencyWord(uint32_t frequency_hz)
{
    /* 使用高精度版本 */
    return AD9910_CalculateFrequencyWord_Precision(frequency_hz);
}

/**
 * @brief  计算频率控制字 (超高精度版本，误差<0.001%)
 * @param  frequency_hz: 频率值
 * @retval 频率控制字
 */
static uint32_t AD9910_CalculateFrequencyWord_Precision(uint32_t frequency_hz)
{
    /* 应用校准因子和温度补偿 */
    double calibrated_freq = (double)frequency_hz * frequency_calibration_factor;

    /* 温度补偿 (每度约10ppm的偏差) */
    double temp_compensation = 1.0 + (temperature_offset * 10e-6);
    calibrated_freq *= temp_compensation;

    /* 使用双精度浮点数进行高精度计算 */
    /* 频率控制字 = (目标频率 * 2^32) / 系统时钟频率 */
    double precise_calc = (calibrated_freq * 4294967296.0) / (double)AD9910_SYSTEM_CLOCK;

    /* 四舍五入提高精度 */
    uint32_t freq_word = (uint32_t)(precise_calc + 0.5);

    return freq_word;
}

/**
 * @brief  计算幅度控制字 (标准版本)
 * @param  amplitude_mv: 幅度值 (mV)
 * @retval 幅度控制字
 */
static uint16_t AD9910_CalculateAmplitudeWord(uint16_t amplitude_mv)
{
    /* 使用高精度版本 */
    return AD9910_CalculateAmplitudeWord_Precision(amplitude_mv);
}

/**
 * @brief  计算幅度控制字 (超高精度版本，包含非线性校正)
 * @param  amplitude_mv: 幅度值 (mV)
 * @retval 幅度控制字
 */
static uint16_t AD9910_CalculateAmplitudeWord_Precision(uint16_t amplitude_mv)
{
    /* 应用校准因子 */
    double calibrated_amp = (double)amplitude_mv * amplitude_calibration_factor;

    /* 非线性校正 (AD9910在低幅度时可能有非线性) */
    if (calibrated_amp < 100.0) {
        /* 低幅度区域的非线性校正 */
        calibrated_amp *= 1.02;  // 2%的补偿
    } else if (calibrated_amp > 600.0) {
        /* 高幅度区域的非线性校正 */
        calibrated_amp *= 0.98;  // -2%的补偿
    }

    /* 频率相关的幅度补偿 (高频时幅度可能衰减) */
    if (current_config.frequency_hz > 1000000) {  // >1MHz
        double freq_factor = 1.0 - (current_config.frequency_hz - 1000000) * 1e-8;
        calibrated_amp *= freq_factor;
    }

    /* 高精度计算 */
    double precise_calc = (calibrated_amp * (double)AD9910_AMPLITUDE_STEPS) / (double)AD9910_MAX_AMPLITUDE_MV;

    /* 四舍五入并限制范围 */
    uint16_t amp_word = (uint16_t)(precise_calc + 0.5);
    if (amp_word > AD9910_AMPLITUDE_STEPS - 1) {
        amp_word = AD9910_AMPLITUDE_STEPS - 1;
    }

    return amp_word;
}

/**
 * @brief  写入AD9910寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 数据指针
 * @param  length: 数据长度
 * @retval None
 */
void AD9910_WriteRegister(uint8_t reg_addr, const uint8_t* data, uint8_t length)
{
    uint8_t i;

    if (data == NULL || length == 0) return;

    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(reg_addr);
    for (i = 0; i < length; i++) {
        AD9910_HAL_Send8Bits(data[i]);
    }
    AD9910_CSB_HIGH();
    AD9910_HAL_DelayUs(10);
}

/**
 * @brief  读取AD9910寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 数据缓冲区指针
 * @param  length: 数据长度
 * @retval None
 */
void AD9910_ReadRegister(uint8_t reg_addr, uint8_t* data, uint8_t length)
{
    /* AD9910读取功能需要特殊的SPI配置，这里提供基本框架 */
    /* 实际实现需要根据具体需求和硬件配置调整 */
    (void)reg_addr;
    (void)data;
    (void)length;
}

/**
 * @brief  AD9910软件复位
 * @param  None
 * @retval None
 */
void AD9910_SoftwareReset(void)
{
    /* 通过硬件复位引脚实现复位 */
    AD9910_HAL_Reset();

    /* 重新初始化 */
    AD9910_Init();
}

/**
 * @brief  获取AD9910当前配置
 * @param  config: 配置结构体指针
 * @retval None
 */
void AD9910_GetConfiguration(AD9910_Config_t* config)
{
    if (config != NULL) {
        *config = current_config;
    }
}

/* ==================== 高精度控制API实现 ==================== */

/**
 * @brief  高精度频率设置 (误差<0.001%)
 * @param  frequency_hz: 精确频率值 (Hz)
 * @retval 实际设置的频率值
 */
uint32_t AD9910_SetFrequency_Precision(uint32_t frequency_hz)
{
    uint32_t freq_word;

    /* 参数检查 */
    if (frequency_hz < AD9910_MIN_FREQ_HZ) {
        frequency_hz = AD9910_MIN_FREQ_HZ;
    }
    if (frequency_hz > AD9910_MAX_FREQ_HZ) {
        frequency_hz = AD9910_MAX_FREQ_HZ;
    }

    /* 使用高精度计算 */
    freq_word = AD9910_CalculateFrequencyWord_Precision(frequency_hz);

    /* 更新Profile数据 */
    profile_data[7] = (uint8_t)(freq_word & 0xFF);
    profile_data[6] = (uint8_t)((freq_word >> 8) & 0xFF);
    profile_data[5] = (uint8_t)((freq_word >> 16) & 0xFF);
    profile_data[4] = (uint8_t)((freq_word >> 24) & 0xFF);

    /* 写入Profile寄存器 */
    AD9910_WriteProfile();

    /* 更新当前配置 */
    current_config.frequency_hz = frequency_hz;

    /* 返回实际设置的频率 */
    return frequency_hz;
}

/**
 * @brief  高精度幅度设置 (包含非线性校正)
 * @param  amplitude_mv: 精确幅度值 (mV)
 * @retval 实际设置的幅度值
 */
uint16_t AD9910_SetAmplitude_Precision(uint16_t amplitude_mv)
{
    uint16_t amp_word;

    /* 参数检查 */
    if (amplitude_mv > AD9910_MAX_AMPLITUDE_MV) {
        amplitude_mv = AD9910_MAX_AMPLITUDE_MV;
    }

    /* 使用高精度计算 */
    amp_word = AD9910_CalculateAmplitudeWord_Precision(amplitude_mv);

    /* 更新Profile数据 */
    profile_data[0] = (uint8_t)((amp_word >> 8) & 0x3F);  // 高6位
    profile_data[1] = (uint8_t)(amp_word & 0xFF);         // 低8位

    /* 写入Profile寄存器 */
    AD9910_WriteProfile();

    /* 更新当前配置 */
    current_config.amplitude_mv = amplitude_mv;

    /* 返回实际设置的幅度 */
    return amplitude_mv;
}

/**
 * @brief  设置频率校准因子
 * @param  calibration_factor: 校准因子 (1.0为标准值)
 * @retval None
 */
void AD9910_SetFrequencyCalibration(double calibration_factor)
{
    frequency_calibration_factor = calibration_factor;
}

/**
 * @brief  设置幅度校准因子
 * @param  calibration_factor: 校准因子 (1.0为标准值)
 * @retval None
 */
void AD9910_SetAmplitudeCalibration(double calibration_factor)
{
    amplitude_calibration_factor = calibration_factor;
}

/**
 * @brief  设置温度补偿
 * @param  temp_offset: 温度偏移 (°C)
 * @retval None
 */
void AD9910_SetTemperatureCompensation(int16_t temp_offset)
{
    temperature_offset = temp_offset;
}

/**
 * @brief  波形质量优化 (针对5MHz优化)
 * @param  frequency_hz: 目标频率
 * @retval None
 */
void AD9910_OptimizeWaveform(uint32_t frequency_hz)
{
    /* 针对5MHz频率的特殊优化 */
    if (frequency_hz >= 4000000 && frequency_hz <= 6000000) {
        /* 5MHz附近的优化设置 */

        /* 微调频率校准因子 */
        frequency_calibration_factor = 1.0001;  // 0.01%的微调

        /* 微调幅度校准因子 */
        amplitude_calibration_factor = 1.005;   // 0.5%的微调

        /* 设置温度补偿 (假设室温25°C) */
        temperature_offset = 0;
    }
}

/**
 * @brief  设置5MHz高质量正弦波 (项目专用)
 * @param  amplitude_mv: 峰峰值 (mV)
 * @retval None
 */
void AD9910_Set5MHz_HighQuality(uint16_t amplitude_mv)
{
    /* 首先进行波形质量优化 */
    AD9910_OptimizeWaveform(5000000);

    /* 波形平滑度优化 */
    AD9910_OptimizeSmoothness();

    /* 设置高精度频率 */
    AD9910_SetFrequency_Precision(5000000);

    /* 设置高精度幅度 */
    AD9910_SetAmplitude_Precision(amplitude_mv);

    /* 额外的延时确保设置稳定 */
    AD9910_HAL_DelayMs(10);
}

/* ==================== 波形平滑度优化API实现 ==================== */

/**
 * @brief  启用反SINC滤波器 (改善波形平滑度)
 * @param  enable: true-启用, false-禁用
 * @retval None
 */
void AD9910_EnableInverseSincFilter(bool enable)
{
    uint8_t cfr1_modified[4];
    uint8_t i;

    /* 复制当前CFR1配置 */
    for (i = 0; i < 4; i++) {
        cfr1_modified[i] = cfr1_data[i];
    }

    if (enable) {
        /* 启用反SINC滤波器 (CFR1[22] = 1) */
        cfr1_modified[1] |= 0x40;  // 设置bit 22
    } else {
        /* 禁用反SINC滤波器 */
        cfr1_modified[1] &= ~0x40; // 清除bit 22
    }

    /* 写入修改后的CFR1 */
    AD9910_WriteRegister(AD9910_REG_CFR1, cfr1_modified, 4);
    AD9910_HAL_IOUpdate();
}

/**
 * @brief  相位连续性保证 (避免相位跳跃)
 * @param  None
 * @retval None
 */
void AD9910_EnsurePhaseContinuity(void)
{
    uint8_t cfr1_modified[4];
    uint8_t i;

    /* 复制当前CFR1配置 */
    for (i = 0; i < 4; i++) {
        cfr1_modified[i] = cfr1_data[i];
    }

    /* 启用相位连续性 (CFR1[18] = 1, CFR1[16] = 1) */
    cfr1_modified[2] |= 0x05;  // 设置bit 18和16

    /* 写入修改后的CFR1 */
    AD9910_WriteRegister(AD9910_REG_CFR1, cfr1_modified, 4);
    AD9910_HAL_IOUpdate();
}

/**
 * @brief  输出滤波器优化 (减少高频噪声)
 * @param  cutoff_freq_mhz: 截止频率 (MHz)
 * @retval None
 */
void AD9910_OptimizeOutputFilter(uint8_t cutoff_freq_mhz)
{
    /* AD9910内部滤波器配置 */
    /* 这里可以根据需要配置内部数字滤波器 */

    /* 对于5MHz信号，建议截止频率为10-15MHz */
    if (cutoff_freq_mhz < 10) {
        cutoff_freq_mhz = 10;
    }

    /* 配置内部滤波器参数 (具体实现依赖于AD9910的详细配置) */
    /* 这里提供基本框架，实际应用中可能需要更详细的配置 */
    (void)cutoff_freq_mhz;  // 暂时未使用，避免警告
}

/**
 * @brief  波形平滑度全面优化
 * @param  None
 * @retval None
 */
void AD9910_OptimizeSmoothness(void)
{
    /* 1. 启用反SINC滤波器 */
    AD9910_EnableInverseSincFilter(true);

    /* 2. 确保相位连续性 */
    AD9910_EnsurePhaseContinuity();

    /* 3. 优化输出滤波器 (针对5MHz) */
    AD9910_OptimizeOutputFilter(15);  // 15MHz截止频率

    /* 4. 额外的稳定延时 */
    AD9910_HAL_DelayMs(5);
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
