/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V3.0
  * @date    2024-08-02
  * @brief   STM32F4控制AD9910 DDS信号发生器 - 外部控制版本
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// AD9910波形产生模块
#include "../Modules/Generation/ad9910_waveform.h"

// 外部控制模块
#include "../Modules/Control/ad9910_control.h"
#include "../Modules/Control/command_parser.h"
#include "../Modules/Control/gain_calculator.h"

// OLED显示模块
#include "../Modules/Interface/oled.h"

// 矩阵键盘模块
#include "../Modules/Interface/matrix_keypad.h"

// 信号发生器控制模块
#include "../Modules/Control/signal_generator.h"

// 串口屏功能已彻底删除

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// AD9910外部控制系统技术规格：
// - 支持串口屏和4x4矩阵键盘控制
// - 实时频率调节：1Hz - 420MHz
// - 实时峰峰值调节：考虑后续电路增益
// - 增益计算算法：自动补偿频率、温度、非线性
// - 预设参数管理：8组预设配置
// - 高精度控制：频率±0.001%，幅度±0.01%

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void ControlCallback(ControlCommand_t cmd, ControlStatus_t status, void* data);
void PrintGainCalculationDetails(uint32_t frequency_hz, uint16_t target_output_mv);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* OLED显示初始化 */
    OLED_Init();

    /* 信号发生器系统初始化 */
    if (SignalGenerator_Init() != 0) {
        // 初始化失败处理
        OLED_Clear();
        OLED_ShowString(0, 0, (u8*)"INIT FAILED");
        OLED_ShowString(0, 2, (u8*)"CHECK HW");
        OLED_Refresh_Gram();
        while(1); // 停止运行
    }

    /* 串口屏功能已彻底删除 */

    /* 外部控制系统初始化 */
    CommandParser_Init();
    GainCalculator_Init();
    // AD9910_Control_Init(); // 已在SignalGenerator_Init中初始化

    /* 注册控制回调函数 */
    AD9910_Control_RegisterCallback(ControlCallback);

    /* 设置基准测试信号：1MHz，0.5V正弦波 */
    // 在信号发生器确认生成波形前，先输出固定的基准信号
    uint32_t baseline_freq = 1000000;  // 1MHz
    uint16_t baseline_output_mv = 500;  // 0.5V峰峰值

    // 计算1MHz频率下的增益
    double transfer_gain = GainCalculator_TransferFunctionGain(baseline_freq);
    double opamp_gain = 6.0;    // 运算放大器增益
    double calculated_total_gain = transfer_gain * opamp_gain;

    AD9910_Control_SetFrequency(baseline_freq);           // 1MHz
    AD9910_Control_SetTargetAmplitude(baseline_output_mv); // 0.5V峰峰值
    AD9910_Control_SetGainFactor(calculated_total_gain);   // 使用计算的总增益
    AD9910_Control_EnableOutput(true);                    // 使能输出

    /* 打印基准信号增益计算详情 */
    PrintGainCalculationDetails(baseline_freq, baseline_output_mv);

    /* 串口屏功能已彻底删除 */

    /* ==================== 外部控制系统就绪 ==================== */
    // 系统已准备好接受外部控制命令
    // 支持串口屏和4x4矩阵键盘控制
    // 支持实时频率、峰峰值、增益调节

    /* 移除测试代码，让信号发生器正常工作 */

    /* 强制确保AD9910输出波形 */
    AD9910_Control_EnableOutput(true);
    AD9910_Control_Task();

    while (1) {
        /* 信号发生器主处理 */
        SignalGenerator_Process();

        /* 控制系统任务处理 - 确保波形持续输出 */
        AD9910_Control_Task();

        /* 串口屏功能已彻底删除 */

        /* 极速模式：完全无延时，工程级响应速度 */
        // 完全移除所有延时，系统全速运行
    }
}

/**
  * @brief  控制命令回调函数 (串口屏功能已彻底删除)
  * @param  cmd: 执行的命令
  * @param  status: 执行状态
  * @param  data: 相关数据
  * @retval None
  */
void ControlCallback(ControlCommand_t cmd, ControlStatus_t status, void* data)
{
    /* 串口屏功能已彻底删除 - 回调函数保留为空 */
    /* 避免未使用参数警告 */
    (void)cmd;
    (void)status;
    (void)data;
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  打印增益计算详情 (用于验证3V峰峰值输出)
  * @param  frequency_hz: 工作频率
  * @param  target_output_mv: 目标输出峰峰值 (mV)
  * @retval None
  */
void PrintGainCalculationDetails(uint32_t frequency_hz, uint16_t target_output_mv)
{
    // 计算传递函数H(s)在指定频率下的增益
    double transfer_gain = GainCalculator_TransferFunctionGain(frequency_hz);

    // 运算放大器增益
    double opamp_gain = 6.0;

    // 总增益
    double total_gain = transfer_gain * opamp_gain;

    // 计算AD9910所需输出
    uint16_t required_ad9910_output = GainCalculator_GetAD9910Output(target_output_mv, total_gain);

    // 验证最终输出
    uint16_t calculated_final_output = GainCalculator_GetFinalOutput(required_ad9910_output, total_gain);

    // 计算误差
    double error_percent = ((double)calculated_final_output - target_output_mv) / target_output_mv * 100.0;

    // 这里可以通过串口或其他方式输出调试信息
    // 由于没有printf实现，我们将信息存储在静态变量中供调试器查看
    static char debug_info[512];
    sprintf(debug_info,
        "=== 3kHz Gain Verification for 3V Peak-to-Peak Output ===\n"
        "Working Frequency: %luHz\n"
        "Target Output: %dmV (3V peak-to-peak)\n"
        "Transfer Function H(s) Gain: %.6f\n"
        "OpAmp Circuit Gain: %.1f\n"
        "Total Gain: %.6f\n"
        "Required AD9910 Output: %dmV\n"
        "Calculated Final Output: %dmV\n"
        "Error: %.4f%%\n"
        "Feasibility: %s\n"
        "========================================\n",
        frequency_hz, target_output_mv,
        transfer_gain, opamp_gain, total_gain,
        required_ad9910_output, calculated_final_output, error_percent,
        (required_ad9910_output <= 800) ? "OK (within AD9910 range)" : "FAIL (exceeds AD9910 range)");

    // 防止编译器优化掉debug_info
    (void)debug_info;
}















/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


