/**
  ******************************************************************************
  * @file    ad9854_control.c
  * <AUTHOR> - AD9854适配器
  * @version V1.0
  * @date    2024
  * @brief   AD9854控制适配器实现 - 包装现有AD9854驱动
  ******************************************************************************
  */

#include "ad9854_control.h"
#include "../Generation/ad9854.h"  // 使用现有的AD9854驱动
#include <string.h>

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

/**
  * @brief  初始化标志
  */
volatile bool g_ad9854_control_initialized = false;

/**
  * @brief  控制参数
  */
static AD9854_Control_Params_t s_control_params = {
    .frequency_hz = AD9854_FREQ_DEFAULT_HZ,
    .amplitude_mv = AD9854_AMP_DEFAULT_MV,
    .wave_type = AD9854_WAVE_SINE,
    .output_enabled = false,
    .initialized = false
};

/* Private function prototypes -----------------------------------------------*/

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  AD9854控制模块初始化
  * @param  None
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_Init(void)
{
    // 调用现有的AD9854初始化函数
    if (AD9854_Init() != AD9854_OK) {
        return CONTROL_STATUS_ERROR;
    }

    // 设置默认参数
    s_control_params.frequency_hz = AD9854_FREQ_DEFAULT_HZ;
    s_control_params.amplitude_mv = AD9854_AMP_DEFAULT_MV;
    s_control_params.wave_type = AD9854_WAVE_SINE;
    s_control_params.output_enabled = true;
    s_control_params.initialized = true;

    // 设置默认频率 (5MHz)
    AD9854_SetTargetFrequency((double)AD9854_FREQ_DEFAULT_HZ);

    // 设置默认幅度 (0.5V峰峰值)
    AD9854_SetTargetAmplitude((double)AD9854_AMP_DEFAULT_MV);

    // 使能输出
    AD9854_EnableOutput(1);

    g_ad9854_control_initialized = true;

    return CONTROL_STATUS_OK;
}

/**
  * @brief  AD9854控制模块反初始化
  * @param  None
  * @retval None
  */
void AD9854_Control_DeInit(void)
{
    // 禁用输出
    AD9854_Control_EnableOutput(false);
    
    // 重置参数
    memset(&s_control_params, 0, sizeof(s_control_params));
    
    g_ad9854_control_initialized = false;
}

/**
  * @brief  设置输出频率
  * @param  frequency_hz: 频率值 (Hz)
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_SetFrequency(uint32_t frequency_hz)
{
    if (!g_ad9854_control_initialized) {
        return CONTROL_STATUS_NOT_READY;
    }

    if (!AD9854_IS_FREQ_VALID(frequency_hz)) {
        return CONTROL_STATUS_INVALID_PARAM;
    }

    // 调用现有的AD9854设置频率函数
    if (AD9854_SetTargetFrequency((double)frequency_hz) != AD9854_OK) {
        return CONTROL_STATUS_ERROR;
    }

    s_control_params.frequency_hz = frequency_hz;

    return CONTROL_STATUS_OK;
}

/**
  * @brief  设置目标幅度 (通过外部电路调整)
  * @param  target_amplitude_mv: 目标幅度 (mV)
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_SetTargetAmplitude(uint16_t target_amplitude_mv)
{
    if (!g_ad9854_control_initialized) {
        return CONTROL_STATUS_NOT_READY;
    }

    if (!AD9854_IS_AMP_VALID(target_amplitude_mv)) {
        return CONTROL_STATUS_INVALID_PARAM;
    }

    // 调用现有的AD9854设置幅度函数
    if (AD9854_SetTargetAmplitude((double)target_amplitude_mv) != AD9854_OK) {
        return CONTROL_STATUS_ERROR;
    }

    s_control_params.amplitude_mv = target_amplitude_mv;

    return CONTROL_STATUS_OK;
}

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_SetWaveType(AD9854_Wave_t wave_type)
{
    if (!g_ad9854_control_initialized) {
        return CONTROL_STATUS_NOT_READY;
    }
    
    // AD9854主要支持正弦波，其他波形可以通过调制实现
    // 这里先记录波形类型，实际实现可以后续扩展
    s_control_params.wave_type = wave_type;
    
    // 对于非正弦波，可以在这里添加调制逻辑
    // 目前只支持正弦波
    if (wave_type != AD9854_WAVE_SINE) {
        // 可以在这里添加警告或者通过其他方式实现非正弦波
        // 暂时返回成功，但实际还是输出正弦波
    }
    
    return CONTROL_STATUS_OK;
}

/**
  * @brief  使能/禁用输出
  * @param  enable: true-使能, false-禁用
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_EnableOutput(bool enable)
{
    if (!g_ad9854_control_initialized) {
        return CONTROL_STATUS_NOT_READY;
    }

    // 调用现有的AD9854输出使能函数
    if (AD9854_EnableOutput(enable ? 1 : 0) != AD9854_OK) {
        return CONTROL_STATUS_ERROR;
    }

    s_control_params.output_enabled = enable;

    return CONTROL_STATUS_OK;
}

/**
  * @brief  获取当前参数
  * @param  None
  * @retval 参数结构体指针
  */
const AD9854_Control_Params_t* AD9854_Control_GetParams(void)
{
    return &s_control_params;
}

/**
  * @brief  获取当前频率
  * @param  None
  * @retval 当前频率 (Hz)
  */
uint32_t AD9854_Control_GetFrequency(void)
{
    return s_control_params.frequency_hz;
}

/**
  * @brief  获取当前幅度
  * @param  None
  * @retval 当前幅度 (mV)
  */
uint16_t AD9854_Control_GetAmplitude(void)
{
    return s_control_params.amplitude_mv;
}

/**
  * @brief  获取当前波形类型
  * @param  None
  * @retval 当前波形类型
  */
AD9854_Wave_t AD9854_Control_GetWaveType(void)
{
    return s_control_params.wave_type;
}

/**
  * @brief  检查输出是否使能
  * @param  None
  * @retval true: 使能, false: 禁用
  */
bool AD9854_Control_IsOutputEnabled(void)
{
    return s_control_params.output_enabled;
}

/**
  * @brief  检查模块是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool AD9854_Control_IsReady(void)
{
    return g_ad9854_control_initialized && s_control_params.initialized;
}

/**
  * @brief  复位AD9854
  * @param  None
  * @retval CONTROL_STATUS_OK: 成功, 其他: 失败
  */
ControlStatus_t AD9854_Control_Reset(void)
{
    if (!g_ad9854_control_initialized) {
        return CONTROL_STATUS_NOT_READY;
    }
    
    // 调用现有的AD9854复位函数
    AD9854_Reset();
    
    // 重新设置参数
    AD9854_SetFrequency(s_control_params.frequency_hz);
    AD9854_SetAmplitude(s_control_params.amplitude_mv);
    
    return CONTROL_STATUS_OK;
}

/**
  * @brief  获取设备信息字符串
  * @param  None
  * @retval 设备信息字符串
  */
const char* AD9854_Control_GetDeviceInfo(void)
{
    return "AD9854 DDS Signal Generator";
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
