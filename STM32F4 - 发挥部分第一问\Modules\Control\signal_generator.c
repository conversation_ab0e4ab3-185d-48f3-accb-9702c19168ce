/**
  ******************************************************************************
  * @file    signal_generator.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   信号发生器控制模块实现
  ******************************************************************************
  */

#include "signal_generator.h"
#include "ad9910_control.h"
#include "../Generation/ad9910_waveform.h"
#include "../Generation/ad9910_hal.h"
#include "../Core/systick.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

/**
  * @brief  初始化标志
  */
volatile bool g_signal_generator_initialized = false;

/**
  * @brief  信号发生器参数
  */
static Signal_Generator_t s_sig_gen = {
    .frequency_hz = SIG_GEN_FREQ_DEFAULT_HZ,
    .amplitude_mv = SIG_GEN_AMP_DEFAULT_MV,
    .wave_type = WAVE_SINE,
    .work_mode = MODE_NORMAL,
    .edit_state = EDIT_STATE_NONE,
    .edit_position = 0,
    .freq_buffer = {0},
    .amp_buffer = {0},
    .buffer_index = 0,
    .parameter_changed = true,
    .display_update_needed = true,
    .freq_unit = FREQ_UNIT_HZ,
    .amp_unit = AMP_UNIT_V
};

/**
  * @brief  测量结果
  */
static Measurement_Result_t s_measurement = {
    .input_freq_hz = 0,
    .input_amp_mv = 0,
    .phase_deg = 0.0f,
    .gain_db = 0.0f,
    .valid = false
};

/**
  * @brief  波形名称表
  */
static const char* s_wave_names[WAVE_COUNT] = {
    "SIN", "SQR", "TRI", "SAW"
};

/**
  * @brief  频率单位名称表
  */
static const char* s_freq_unit_names[FREQ_UNIT_COUNT] = {
    "Hz", "kHz", "MHz"
};

/**
  * @brief  幅度单位名称表
  */
static const char* s_amp_unit_names[AMP_UNIT_COUNT] = {
    "mV", "V"
};

/**
  * @brief  模式名称表 (预留用于调试)
  */
// static const char* s_mode_names[MODE_COUNT] = {
//     "NORMAL", "FREQ", "AMP", "WAVE", "MEASURE"
// };

/**
  * @brief  上次显示更新时间 (已移除时间限制，保留用于扩展)
  */
// static uint32_t s_last_display_update = 0;

/**
  * @brief  上次按键扫描时间 (已移除时间限制，保留用于扩展)
  */
// static uint32_t s_last_key_scan = 0;

/**
  * @brief  上次按键时间 (已移除防重复机制)
  */
// static uint32_t s_last_key_time = 0;

/**
  * @brief  上次按键编码 (已移除防重复机制)
  */
// static uint8_t s_last_key_code = 0;
// static Keypad_ID_t s_last_keypad_id = KEYPAD_1;

/**
  * @brief  显示缓存 - 用于差分更新
  */
static struct {
    uint32_t last_frequency;
    uint16_t last_amplitude;
    Wave_Type_t last_wave_type;
    Work_Mode_t last_work_mode;
    bool frequency_changed;
    bool amplitude_changed;
    bool wave_changed;
    bool mode_changed;
    bool force_full_update;
} s_display_cache = {0};

/* Private function prototypes -----------------------------------------------*/

static void SignalGenerator_ProcessKeypad1(uint8_t key_code);
static void SignalGenerator_ProcessKeypad2(uint8_t key_code);
static void SignalGenerator_UpdateParameters(void);
static void SignalGenerator_DisplayMain(void);
static void SignalGenerator_DisplayFreqSet(void);
static void SignalGenerator_DisplayAmpSet(void);
static void SignalGenerator_DisplayWaveSet(void);
static void SignalGenerator_DisplayMeasurement(void);
// static void SignalGenerator_UpdateDisplayDiff(void); // 已移除
static bool SignalGenerator_ProcessDigitInput(char digit);
static void SignalGenerator_ClearInputBuffer(void);
static uint32_t SignalGenerator_ParseFrequency(const char* buffer);
static uint16_t SignalGenerator_ParseAmplitude(const char* buffer);
static uint32_t SignalGenerator_ParseFrequencyWithUnit(const char* buffer, Freq_Unit_t unit);
static uint16_t SignalGenerator_ParseAmplitudeWithUnit(const char* buffer, Amp_Unit_t unit);

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  信号发生器初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SignalGenerator_Init(void)
{
    if (g_signal_generator_initialized) {
        return 0;
    }
    
    // 初始化矩阵键盘
    if (MatrixKeypad_Init() != 0) {
        return -1;
    }
    
    // 初始化OLED显示
    // OLED_Init(); // 假设已经在main中初始化
    
    // 初始化AD9910控制
    if (AD9910_Control_Init() != 0) {
        return -1;
    }
    
    // 设置初始参数
    SignalGenerator_UpdateParameters();

    // 初始化显示缓存 (消除编译器警告)
    (void)s_display_cache; // 标记变量已使用
    s_display_cache.force_full_update = true;
    s_display_cache.last_frequency = 0;
    s_display_cache.last_amplitude = 0;
    s_display_cache.last_wave_type = WAVE_COUNT; // 无效值，强制更新
    s_display_cache.last_work_mode = MODE_COUNT; // 无效值，强制更新

    g_signal_generator_initialized = true;

    // 强制立即显示初始界面
    s_sig_gen.display_update_needed = true;
    SignalGenerator_UpdateDisplay();

    return 0;
}

/**
  * @brief  信号发生器反初始化
  * @param  None
  * @retval None
  */
void SignalGenerator_DeInit(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }
    
    MatrixKeypad_DeInit();
    // AD9910_Control_DeInit(); // 如果有的话
    
    g_signal_generator_initialized = false;
}

/**
  * @brief  信号发生器主循环处理
  * @param  None
  * @retval None
  */
void SignalGenerator_Process(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }

    uint32_t current_time = SysTick_GetTick();

    // 极简按键扫描 - 无任何限制和防重复
    Key_Event_t key_event;
    if (MatrixKeypad_ScanAll(&key_event)) {
        if (key_event.state == KEY_STATE_PRESSED) {
            // 立即处理按键 (按键处理函数内部已包含显示更新)
            if (key_event.keypad_id == KEYPAD_1) {
                SignalGenerator_ProcessKeypad1(key_event.key_code);
            } else if (key_event.keypad_id == KEYPAD_2) {
                SignalGenerator_ProcessKeypad2(key_event.key_code);
            }
            // 显示更新已在按键处理函数中完成
        }
    }

    // 显示更新已移至按键处理中，立即响应
    // 这里不再需要检查display_update_needed，因为按键处理时就立即更新了

    // 极速参数更新 - 立即更新，无任何延时
    if (s_sig_gen.parameter_changed) {
        SignalGenerator_UpdateParameters();
        s_sig_gen.parameter_changed = false;
    }
}

/**
  * @brief  设置频率
  * @param  frequency_hz: 频率值 (Hz)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetFrequency(uint32_t frequency_hz)
{
    if (!SIG_GEN_IS_FREQ_VALID(frequency_hz)) {
        return false;
    }

    // 只有频率真正改变时才更新
    if (s_sig_gen.frequency_hz != frequency_hz) {
        s_sig_gen.frequency_hz = frequency_hz;
        s_sig_gen.parameter_changed = true;
        s_sig_gen.display_update_needed = true;
        // 标记频率变化
        s_display_cache.frequency_changed = true;
    }

    return true;
}

/**
  * @brief  设置幅度
  * @param  amplitude_mv: 幅度值 (mV)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetAmplitude(uint16_t amplitude_mv)
{
    if (!SIG_GEN_IS_AMP_VALID(amplitude_mv)) {
        return false;
    }

    // 只有幅度真正改变时才更新
    if (s_sig_gen.amplitude_mv != amplitude_mv) {
        s_sig_gen.amplitude_mv = amplitude_mv;
        s_sig_gen.parameter_changed = true;
        s_sig_gen.display_update_needed = true;
        // 标记幅度变化
        s_display_cache.amplitude_changed = true;
    }

    return true;
}

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetWaveType(Wave_Type_t wave_type)
{
    if (wave_type >= WAVE_COUNT) {
        return false;
    }

    // 只有波形真正改变时才更新
    if (s_sig_gen.wave_type != wave_type) {
        s_sig_gen.wave_type = wave_type;
        s_sig_gen.parameter_changed = true;
        s_sig_gen.display_update_needed = true;
        // 标记波形变化
        s_display_cache.wave_changed = true;
    }

    return true;
}

/**
  * @brief  获取当前参数
  * @param  None
  * @retval 信号发生器参数结构体指针
  */
const Signal_Generator_t* SignalGenerator_GetParams(void)
{
    return &s_sig_gen;
}

/**
  * @brief  开始测量模式
  * @param  None
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_StartMeasurement(void)
{
    s_sig_gen.work_mode = MODE_MEASURE;
    s_sig_gen.display_update_needed = true;
    
    // 这里可以添加实际的测量逻辑
    // 暂时使用模拟数据
    s_measurement.input_freq_hz = s_sig_gen.frequency_hz;
    s_measurement.input_amp_mv = s_sig_gen.amplitude_mv;
    s_measurement.phase_deg = 45.0f;
    s_measurement.gain_db = -3.0f;
    s_measurement.valid = true;
    
    return true;
}

/**
  * @brief  获取测量结果
  * @param  None
  * @retval 测量结果结构体指针
  */
const Measurement_Result_t* SignalGenerator_GetMeasurement(void)
{
    return &s_measurement;
}

/**
  * @brief  强制更新显示
  * @param  None
  * @retval None
  */
void SignalGenerator_UpdateDisplay(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }

    // 智能差分更新 - 只在必要时全屏刷新
    bool need_full_update = s_display_cache.force_full_update ||
                           (s_sig_gen.work_mode != s_display_cache.last_work_mode);

    if (need_full_update) {
        // 全屏更新
        OLED_Clear();
    }

    switch (s_sig_gen.work_mode) {
        case MODE_NORMAL:
            SignalGenerator_DisplayMain();
            break;
        case MODE_FREQ_SET:
            SignalGenerator_DisplayFreqSet();
            break;
        case MODE_AMP_SET:
            SignalGenerator_DisplayAmpSet();
            break;
        case MODE_WAVE_SEL:
            SignalGenerator_DisplayWaveSet();
            break;
        case MODE_MEASURE:
            SignalGenerator_DisplayMeasurement();
            break;
        default:
            SignalGenerator_DisplayMain();
            break;
    }

    // 立即刷新，无任何延时
    OLED_Refresh_Gram();

    // 更新缓存
    s_display_cache.last_frequency = s_sig_gen.frequency_hz;
    s_display_cache.last_amplitude = s_sig_gen.amplitude_mv;
    s_display_cache.last_wave_type = s_sig_gen.wave_type;
    s_display_cache.last_work_mode = s_sig_gen.work_mode;
    s_display_cache.force_full_update = false;

    // 清除变化标志
    s_display_cache.frequency_changed = false;
    s_display_cache.amplitude_changed = false;
    s_display_cache.wave_changed = false;
    s_display_cache.mode_changed = false;
}

/**
  * @brief  检查是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool SignalGenerator_IsReady(void)
{
    return g_signal_generator_initialized;
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  处理第一块键盘输入 (数字输入键盘)
  * @param  key_code: 按键编码
  * @retval None
  */
static void SignalGenerator_ProcessKeypad1(uint8_t key_code)
{
    switch (key_code) {
        case KEY1_1: case KEY1_2: case KEY1_3:
        case KEY1_4: case KEY1_5: case KEY1_6:
        case KEY1_7: case KEY1_8: case KEY1_9:
        case KEY1_0:
            // 数字输入 - 修复按键映射
            if (s_sig_gen.edit_state == EDIT_STATE_INPUT) {
                char digit;
                // 修复：正确的按键到数字的映射
                switch (key_code) {
                    case KEY1_0: digit = '0'; break;
                    case KEY1_1: digit = '1'; break;
                    case KEY1_2: digit = '2'; break;
                    case KEY1_3: digit = '3'; break;
                    case KEY1_4: digit = '4'; break;
                    case KEY1_5: digit = '5'; break;
                    case KEY1_6: digit = '6'; break;
                    case KEY1_7: digit = '7'; break;
                    case KEY1_8: digit = '8'; break;
                    case KEY1_9: digit = '9'; break;
                    default: break;  // 无效按键
                }

                if (SignalGenerator_ProcessDigitInput(digit)) {
                    // 输入成功，立即更新显示
                    s_sig_gen.display_update_needed = true;
                    s_display_cache.force_full_update = true;
                    // 立即更新显示
                    SignalGenerator_UpdateDisplay();
                }
            }
            break;

        case KEY1_STAR:
            // 清除当前输入
            SignalGenerator_ClearInputBuffer();
            // 立即更新显示
            s_sig_gen.display_update_needed = true;
            s_display_cache.force_full_update = true;
            break;

        case KEY1_HASH:
            // 全局确认键 - 立即应用当前参数并生成新波形
            if (s_sig_gen.edit_state == EDIT_STATE_INPUT) {
                // 如果在输入模式，确认输入
                if (s_sig_gen.work_mode == MODE_FREQ_SET) {
                    uint32_t freq = SignalGenerator_ParseFrequency(s_sig_gen.freq_buffer);
                    if (SIG_GEN_IS_FREQ_VALID(freq)) {
                        SignalGenerator_SetFrequency(freq);
                    }
                } else if (s_sig_gen.work_mode == MODE_AMP_SET) {
                    uint16_t amp = SignalGenerator_ParseAmplitude(s_sig_gen.amp_buffer);
                    if (SIG_GEN_IS_AMP_VALID(amp)) {
                        SignalGenerator_SetAmplitude(amp);
                    }
                }
            }

            // 无论什么模式，都立即应用参数并返回正常模式
            s_sig_gen.work_mode = MODE_NORMAL;
            s_sig_gen.edit_state = EDIT_STATE_NONE;

            // 直接更新AD9910参数，确保立即生效
            s_sig_gen.parameter_changed = true;
            SignalGenerator_UpdateParameters();

            // 强制全屏更新显示
            s_display_cache.force_full_update = true;
            s_sig_gen.display_update_needed = true;
            SignalGenerator_UpdateDisplay();
            break;

        case KEY1_FREQ_UP:
            // 频率增加 - 直接修改并立即显示
            if (s_sig_gen.frequency_hz <= SIG_GEN_FREQ_MAX_HZ - SIG_GEN_FREQ_STEP_HZ) {
                s_sig_gen.frequency_hz += SIG_GEN_FREQ_STEP_HZ;
            } else {
                s_sig_gen.frequency_hz = SIG_GEN_FREQ_MAX_HZ;
            }
            // 立即更新AD9910和显示
            s_sig_gen.parameter_changed = true;
            SignalGenerator_UpdateParameters();
            SignalGenerator_UpdateDisplay();
            break;

        case KEY1_FREQ_DOWN:
            // 频率减少 - 直接修改并立即显示
            if (s_sig_gen.frequency_hz > SIG_GEN_FREQ_STEP_HZ &&
                s_sig_gen.frequency_hz - SIG_GEN_FREQ_STEP_HZ >= SIG_GEN_FREQ_MIN_HZ) {
                s_sig_gen.frequency_hz -= SIG_GEN_FREQ_STEP_HZ;
            } else {
                s_sig_gen.frequency_hz = SIG_GEN_FREQ_MIN_HZ;
            }
            // 立即更新AD9910和显示
            s_sig_gen.parameter_changed = true;
            SignalGenerator_UpdateParameters();
            SignalGenerator_UpdateDisplay();
            break;

        case KEY1_AMP_UP:
            // 幅度增加 - 直接修改并立即显示
            if (s_sig_gen.amplitude_mv <= SIG_GEN_AMP_MAX_MV - SIG_GEN_AMP_STEP_MV) {
                s_sig_gen.amplitude_mv += SIG_GEN_AMP_STEP_MV;
            } else {
                s_sig_gen.amplitude_mv = SIG_GEN_AMP_MAX_MV;
            }
            // 立即更新AD9910和显示
            s_sig_gen.parameter_changed = true;
            SignalGenerator_UpdateParameters();
            SignalGenerator_UpdateDisplay();
            break;

        case KEY1_AMP_DOWN:
            // 幅度减少 - 直接修改并立即显示
            if (s_sig_gen.amplitude_mv > SIG_GEN_AMP_STEP_MV &&
                s_sig_gen.amplitude_mv - SIG_GEN_AMP_STEP_MV >= SIG_GEN_AMP_MIN_MV) {
                s_sig_gen.amplitude_mv -= SIG_GEN_AMP_STEP_MV;
            } else {
                s_sig_gen.amplitude_mv = SIG_GEN_AMP_MIN_MV;
            }
            // 立即更新AD9910和显示
            s_sig_gen.parameter_changed = true;
            SignalGenerator_UpdateParameters();
            SignalGenerator_UpdateDisplay();
            break;

        default:
            break;
    }
}

/**
  * @brief  处理第二块键盘输入 (功能控制键盘)
  * @param  key_code: 按键编码
  * @retval None
  */
static void SignalGenerator_ProcessKeypad2(uint8_t key_code)
{
    switch (key_code) {
        case KEY2_FREQ_SET:
            // 进入频率设置模式
            s_sig_gen.work_mode = MODE_FREQ_SET;
            s_sig_gen.edit_state = EDIT_STATE_INPUT;
            SignalGenerator_ClearInputBuffer();
            // 强制全屏更新
            s_display_cache.force_full_update = true;
            s_sig_gen.display_update_needed = true;
            SignalGenerator_UpdateDisplay();  // 立即更新显示
            break;

        case KEY2_AMP_SET:
            // 进入幅度设置模式
            s_sig_gen.work_mode = MODE_AMP_SET;
            s_sig_gen.edit_state = EDIT_STATE_INPUT;
            SignalGenerator_ClearInputBuffer();
            // 强制全屏更新
            s_display_cache.force_full_update = true;
            s_sig_gen.display_update_needed = true;
            SignalGenerator_UpdateDisplay();  // 立即更新显示
            break;

        case KEY2_UNIT_SWITCH:
            // 单位切换
            if (s_sig_gen.work_mode == MODE_FREQ_SET) {
                s_sig_gen.freq_unit = (Freq_Unit_t)((s_sig_gen.freq_unit + 1) % FREQ_UNIT_COUNT);
            } else if (s_sig_gen.work_mode == MODE_AMP_SET) {
                s_sig_gen.amp_unit = (Amp_Unit_t)((s_sig_gen.amp_unit + 1) % AMP_UNIT_COUNT);
            }
            s_display_cache.force_full_update = true;
            s_sig_gen.display_update_needed = true;
            SignalGenerator_UpdateDisplay();  // 立即更新显示
            break;

        case KEY2_CONFIRM:
            // 确认当前设置
            s_sig_gen.work_mode = MODE_NORMAL;
            s_sig_gen.edit_state = EDIT_STATE_NONE;
            s_display_cache.force_full_update = true;
            s_sig_gen.display_update_needed = true;
            SignalGenerator_UpdateDisplay();  // 立即更新显示
            break;

        case KEY2_HZ:
            if (s_sig_gen.work_mode == MODE_FREQ_SET) {
                s_sig_gen.freq_unit = FREQ_UNIT_HZ;
                s_display_cache.force_full_update = true;
            }
            break;

        case KEY2_KHZ:
            if (s_sig_gen.work_mode == MODE_FREQ_SET) {
                s_sig_gen.freq_unit = FREQ_UNIT_KHZ;
                s_display_cache.force_full_update = true;
            }
            break;

        case KEY2_MHZ:
            if (s_sig_gen.work_mode == MODE_FREQ_SET) {
                s_sig_gen.freq_unit = FREQ_UNIT_MHZ;
                s_display_cache.force_full_update = true;
            }
            break;

        case KEY2_VOLT:
            if (s_sig_gen.work_mode == MODE_AMP_SET) {
                s_sig_gen.amp_unit = (Amp_Unit_t)((s_sig_gen.amp_unit + 1) % AMP_UNIT_COUNT);
                s_display_cache.force_full_update = true;
            }
            break;

        case KEY2_SIN:
            // 输出正弦波 - 极速响应
            SignalGenerator_SetWaveType(WAVE_SINE);
            SignalGenerator_UpdateParameters();
            SignalGenerator_UpdateDisplay();
            break;

        case KEY2_SQUARE:
            // 输出方波 - 极速响应
            SignalGenerator_SetWaveType(WAVE_SQUARE);
            SignalGenerator_UpdateParameters();
            SignalGenerator_UpdateDisplay();
            break;

        case KEY2_TRIANGLE:
            // 输出三角波 - 极速响应
            SignalGenerator_SetWaveType(WAVE_TRIANGLE);
            SignalGenerator_UpdateParameters();
            SignalGenerator_UpdateDisplay();
            break;

        case KEY2_SAWTOOTH:
            // 输出锯齿波 - 极速响应
            SignalGenerator_SetWaveType(WAVE_SAWTOOTH);
            SignalGenerator_UpdateParameters();
            SignalGenerator_UpdateDisplay();
            break;

        case KEY2_CLEAR:
            // 清除输入
            SignalGenerator_ClearInputBuffer();
            s_display_cache.force_full_update = true;
            break;

        case KEY2_BACKSPACE:
            // 退格
            if (s_sig_gen.edit_state == EDIT_STATE_INPUT) {
                if (s_sig_gen.work_mode == MODE_FREQ_SET && s_sig_gen.buffer_index > 0) {
                    s_sig_gen.buffer_index--;
                    s_sig_gen.freq_buffer[s_sig_gen.buffer_index] = '\0';
                    s_display_cache.force_full_update = true;
                } else if (s_sig_gen.work_mode == MODE_AMP_SET && s_sig_gen.buffer_index > 0) {
                    s_sig_gen.buffer_index--;
                    s_sig_gen.amp_buffer[s_sig_gen.buffer_index] = '\0';
                    s_display_cache.force_full_update = true;
                }
            }
            break;

        case KEY2_ENTER:
            // 确认输入
            if (s_sig_gen.edit_state == EDIT_STATE_INPUT) {
                if (s_sig_gen.work_mode == MODE_FREQ_SET) {
                    uint32_t freq = SignalGenerator_ParseFrequencyWithUnit(s_sig_gen.freq_buffer, s_sig_gen.freq_unit);
                    if (SIG_GEN_IS_FREQ_VALID(freq)) {
                        SignalGenerator_SetFrequency(freq);
                    }
                } else if (s_sig_gen.work_mode == MODE_AMP_SET) {
                    uint16_t amp = SignalGenerator_ParseAmplitudeWithUnit(s_sig_gen.amp_buffer, s_sig_gen.amp_unit);
                    if (SIG_GEN_IS_AMP_VALID(amp)) {
                        SignalGenerator_SetAmplitude(amp);
                    }
                }
            }
            s_sig_gen.work_mode = MODE_NORMAL;
            s_sig_gen.edit_state = EDIT_STATE_NONE;
            s_display_cache.force_full_update = true;
            break;

        case KEY2_ESCAPE:
            // 退出
            s_sig_gen.work_mode = MODE_NORMAL;
            s_sig_gen.edit_state = EDIT_STATE_NONE;
            s_display_cache.force_full_update = true;
            break;

        default:
            break;
    }

    s_sig_gen.display_update_needed = true;
}

/**
  * @brief  更新AD9910参数
  * @param  None
  * @retval None
  */
static void SignalGenerator_UpdateParameters(void)
{
    // 直接调用AD9910底层API，确保参数立即生效
    AD9910_SetFrequency(s_sig_gen.frequency_hz);
    AD9910_SetAmplitude(s_sig_gen.amplitude_mv);

    // 根据波形类型设置（目前AD9910主要支持正弦波）
    switch (s_sig_gen.wave_type) {
        case WAVE_SINE:
        case WAVE_SQUARE:
        case WAVE_TRIANGLE:
        case WAVE_SAWTOOTH:
        default:
            // AD9910主要输出正弦波，其他波形通过频率和幅度调节
            break;
    }

    // 确保输出使能
    AD9910_EnableOutput();

    // 强制I/O更新，确保参数立即生效
    AD9910_HAL_IOUpdate();
}

/**
  * @brief  显示主界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayMain(void)
{
    char buffer[32];

    // 显示标题
    OLED_ShowString(0, 0, (u8*)"SIGNAL GEN");

    // 显示频率 - 修复单位显示逻辑
    if (s_sig_gen.frequency_hz >= 1000000) {
        sprintf(buffer, "F:%luMHz", s_sig_gen.frequency_hz / 1000000);
    } else if (s_sig_gen.frequency_hz >= 1000) {
        sprintf(buffer, "F:%lukHz", s_sig_gen.frequency_hz / 1000);  // 修复：正确显示kHz
    } else {
        sprintf(buffer, "F:%luHz", s_sig_gen.frequency_hz);
    }
    OLED_ShowString(0, 2, (u8*)buffer);

    // 显示幅度
    sprintf(buffer, "A:%u.%uV", s_sig_gen.amplitude_mv / 1000,
            (s_sig_gen.amplitude_mv % 1000) / 100);
    OLED_ShowString(0, 4, (u8*)buffer);

    // 显示波形
    sprintf(buffer, "W:%s", s_wave_names[s_sig_gen.wave_type]);
    OLED_ShowString(0, 6, (u8*)buffer);
}

/**
  * @brief  显示频率设置界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayFreqSet(void)
{
    char buffer[32];

    // 显示标题和当前单位
    sprintf(buffer, "FREQ SET (%s)", s_freq_unit_names[s_sig_gen.freq_unit]);
    OLED_ShowString(0, 0, (u8*)buffer);

    if (s_sig_gen.edit_state == EDIT_STATE_INPUT && strlen(s_sig_gen.freq_buffer) > 0) {
        // 显示正在输入的数字
        sprintf(buffer, "Input:%s%s_", s_sig_gen.freq_buffer, s_freq_unit_names[s_sig_gen.freq_unit]);
        OLED_ShowString(0, 2, (u8*)buffer);

        // 显示当前值作为参考
        if (s_sig_gen.frequency_hz >= 1000000) {
            sprintf(buffer, "Now:%luMHz", s_sig_gen.frequency_hz / 1000000);
        } else if (s_sig_gen.frequency_hz >= 1000) {
            sprintf(buffer, "Now:%lukHz", s_sig_gen.frequency_hz / 1000);
        } else {
            sprintf(buffer, "Now:%luHz", s_sig_gen.frequency_hz);
        }
        OLED_ShowString(0, 4, (u8*)buffer);
    } else {
        // 显示当前频率值 - 修复单位显示
        if (s_sig_gen.frequency_hz >= 1000000) {
            sprintf(buffer, "F:%luMHz", s_sig_gen.frequency_hz / 1000000);
        } else if (s_sig_gen.frequency_hz >= 1000) {
            sprintf(buffer, "F:%lukHz", s_sig_gen.frequency_hz / 1000);  // 修复：正确显示kHz
        } else {
            sprintf(buffer, "F:%luHz", s_sig_gen.frequency_hz);
        }
        OLED_ShowString(0, 2, (u8*)buffer);

        sprintf(buffer, "Unit:%s UNIT key", s_freq_unit_names[s_sig_gen.freq_unit]);
        OLED_ShowString(0, 4, (u8*)buffer);
    }

    OLED_ShowString(0, 6, (u8*)"ENT OK ESC EXIT");
}

/**
  * @brief  显示幅度设置界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayAmpSet(void)
{
    char buffer[32];

    // 显示标题和当前单位
    sprintf(buffer, "AMP SET (%s)", s_amp_unit_names[s_sig_gen.amp_unit]);
    OLED_ShowString(0, 0, (u8*)buffer);

    if (s_sig_gen.edit_state == EDIT_STATE_INPUT && strlen(s_sig_gen.amp_buffer) > 0) {
        // 显示正在输入的数字
        sprintf(buffer, "Input:%s%s_", s_sig_gen.amp_buffer, s_amp_unit_names[s_sig_gen.amp_unit]);
        OLED_ShowString(0, 2, (u8*)buffer);

        // 显示当前值作为参考
        sprintf(buffer, "Now:%u.%uV", s_sig_gen.amplitude_mv / 1000,
                (s_sig_gen.amplitude_mv % 1000) / 100);
        OLED_ShowString(0, 4, (u8*)buffer);
    } else {
        // 显示当前幅度值
        if (s_sig_gen.amp_unit == AMP_UNIT_V) {
            sprintf(buffer, "A:%u.%uV", s_sig_gen.amplitude_mv / 1000,
                    (s_sig_gen.amplitude_mv % 1000) / 100);
        } else {
            sprintf(buffer, "A:%umV", s_sig_gen.amplitude_mv);
        }
        OLED_ShowString(0, 2, (u8*)buffer);

        sprintf(buffer, "Unit:%s V key", s_amp_unit_names[s_sig_gen.amp_unit]);
        OLED_ShowString(0, 4, (u8*)buffer);
    }

    OLED_ShowString(0, 6, (u8*)"ENT OK ESC EXIT");
}

/**
  * @brief  显示波形设置界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayWaveSet(void)
{
    char buffer[32];

    OLED_ShowString(0, 0, (u8*)"WAVE SEL");

    sprintf(buffer, "W:%s", s_wave_names[s_sig_gen.wave_type]);
    OLED_ShowString(0, 2, (u8*)buffer);

    OLED_ShowString(0, 4, (u8*)"SIN SQR TRI");
    OLED_ShowString(0, 6, (u8*)"ENT EXIT");
}

/**
  * @brief  显示测量界面
  * @param  None
  * @retval None
  */
static void SignalGenerator_DisplayMeasurement(void)
{
    char buffer[32];

    OLED_ShowString(0, 0, (u8*)"MEASURE");

    if (s_measurement.valid) {
        sprintf(buffer, "F:%luHz", s_measurement.input_freq_hz);
        OLED_ShowString(0, 2, (u8*)buffer);

        sprintf(buffer, "G:%.1fdB", s_measurement.gain_db);
        OLED_ShowString(0, 4, (u8*)buffer);

        sprintf(buffer, "P:%.1f°", s_measurement.phase_deg);
        OLED_ShowString(0, 6, (u8*)buffer);
    } else {
        OLED_ShowString(0, 2, (u8*)"MEASURING...");
        OLED_ShowString(0, 6, (u8*)"ESC EXIT");
    }
}

/**
  * @brief  处理数字输入
  * @param  digit: 输入的数字字符
  * @retval true: 成功, false: 失败
  */
static bool SignalGenerator_ProcessDigitInput(char digit)
{
    if (digit < '0' || digit > '9') {
        return false;
    }

    if (s_sig_gen.work_mode == MODE_FREQ_SET) {
        if (s_sig_gen.buffer_index < sizeof(s_sig_gen.freq_buffer) - 1) {
            s_sig_gen.freq_buffer[s_sig_gen.buffer_index++] = digit;
            s_sig_gen.freq_buffer[s_sig_gen.buffer_index] = '\0';
            s_sig_gen.display_update_needed = true;
            return true;
        }
    } else if (s_sig_gen.work_mode == MODE_AMP_SET) {
        if (s_sig_gen.buffer_index < sizeof(s_sig_gen.amp_buffer) - 1) {
            s_sig_gen.amp_buffer[s_sig_gen.buffer_index++] = digit;
            s_sig_gen.amp_buffer[s_sig_gen.buffer_index] = '\0';
            s_sig_gen.display_update_needed = true;
            return true;
        }
    }

    return false;
}

/**
  * @brief  清除输入缓冲区
  * @param  None
  * @retval None
  */
static void SignalGenerator_ClearInputBuffer(void)
{
    memset(s_sig_gen.freq_buffer, 0, sizeof(s_sig_gen.freq_buffer));
    memset(s_sig_gen.amp_buffer, 0, sizeof(s_sig_gen.amp_buffer));
    s_sig_gen.buffer_index = 0;
    s_sig_gen.display_update_needed = true;
}

/**
  * @brief  解析频率字符串
  * @param  buffer: 频率字符串
  * @retval 频率值 (Hz)
  */
static uint32_t SignalGenerator_ParseFrequency(const char* buffer)
{
    if (buffer == NULL || strlen(buffer) == 0) {
        return s_sig_gen.frequency_hz; // 返回当前值
    }

    uint32_t freq = (uint32_t)atol(buffer);
    return freq;
}

/**
  * @brief  解析幅度字符串
  * @param  buffer: 幅度字符串
  * @retval 幅度值 (mV)
  */
static uint16_t SignalGenerator_ParseAmplitude(const char* buffer)
{
    if (buffer == NULL || strlen(buffer) == 0) {
        return s_sig_gen.amplitude_mv; // 返回当前值
    }

    uint16_t amp = (uint16_t)atoi(buffer);
    return amp;
}

/**
  * @brief  解析带单位的频率字符串
  * @param  buffer: 频率字符串
  * @param  unit: 频率单位
  * @retval 频率值 (Hz)
  */
static uint32_t SignalGenerator_ParseFrequencyWithUnit(const char* buffer, Freq_Unit_t unit)
{
    if (buffer == NULL || strlen(buffer) == 0) {
        return s_sig_gen.frequency_hz; // 返回当前值
    }

    uint32_t freq = (uint32_t)atol(buffer);

    // 根据单位转换为Hz
    switch (unit) {
        case FREQ_UNIT_HZ:
            // 已经是Hz，无需转换
            break;
        case FREQ_UNIT_KHZ:
            freq *= 1000;
            break;
        case FREQ_UNIT_MHZ:
            freq *= 1000000;
            break;
        default:
            break;
    }

    return freq;
}

/**
  * @brief  解析带单位的幅度字符串
  * @param  buffer: 幅度字符串
  * @param  unit: 幅度单位
  * @retval 幅度值 (mV)
  */
static uint16_t SignalGenerator_ParseAmplitudeWithUnit(const char* buffer, Amp_Unit_t unit)
{
    if (buffer == NULL || strlen(buffer) == 0) {
        return s_sig_gen.amplitude_mv; // 返回当前值
    }

    uint16_t amp = (uint16_t)atoi(buffer);

    // 根据单位转换为mV
    switch (unit) {
        case AMP_UNIT_MV:
            // 已经是mV，无需转换
            break;
        case AMP_UNIT_V:
            amp *= 1000; // V转换为mV
            break;
        default:
            break;
    }

    return amp;
}

// 差分更新函数已移除，使用全屏更新提高响应速度


