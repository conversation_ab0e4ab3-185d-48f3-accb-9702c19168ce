/**
  ******************************************************************************
  * @file    ad9910_control.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   AD9910外部控制接口实现文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现AD9910的外部控制接口功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "ad9910_control.h"
#include "../Generation/ad9910_waveform.h"
#include "../Generation/ad9910_hal.h"
#include "../Core/systick.h"
#include <string.h>
#include <math.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* 系统参数 */
static SystemParams_t system_params;

/* 参数范围 */
static ParamRanges_t param_ranges = {
    .min_frequency_hz = CTRL_MIN_FREQUENCY_HZ,
    .max_frequency_hz = CTRL_MAX_FREQUENCY_HZ,
    .min_amplitude_mv = CTRL_MIN_AMPLITUDE_MV,
    .max_amplitude_mv = CTRL_MAX_AMPLITUDE_MV,
    .min_gain_factor = CTRL_MIN_GAIN_FACTOR,
    .max_gain_factor = CTRL_MAX_GAIN_FACTOR
};

/* 预设参数数组 */
static PresetParams_t presets[CTRL_MAX_PRESETS];

/* 控制回调函数 */
static ControlCallback_t control_callback = NULL;

/* 系统状态 */
static bool system_initialized = false;

/* Private function prototypes -----------------------------------------------*/
static void UpdateSystemTime(void);
static ControlStatus_t ValidateFrequency(uint32_t frequency_hz);
static ControlStatus_t ValidateAmplitude(uint16_t amplitude_mv);
static ControlStatus_t ValidateGainFactor(double gain_factor);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  控制系统初始化
 * @param  None
 * @retval ControlStatus_t 初始化状态
 */
ControlStatus_t AD9910_Control_Init(void)
{
    /* 初始化系统参数 */
    system_params.frequency_hz = CTRL_DEFAULT_FREQUENCY;
    system_params.amplitude_mv = CTRL_DEFAULT_AMPLITUDE;
    system_params.gain_factor = CTRL_DEFAULT_GAIN;
    system_params.target_amplitude_mv = CTRL_DEFAULT_AMPLITUDE;
    system_params.output_enabled = false;
    system_params.last_update_time = 0;

    /* 初始化预设参数 */
    memset(presets, 0, sizeof(presets));
    
    /* 设置默认预设 */
    strcpy(presets[0].name, "5MHz_0.5V");
    presets[0].frequency_hz = 5000000;
    presets[0].target_amplitude_mv = 500;
    presets[0].gain_factor = 1.0;
    
    strcpy(presets[1].name, "1MHz_1V");
    presets[1].frequency_hz = 1000000;
    presets[1].target_amplitude_mv = 1000;
    presets[1].gain_factor = 1.0;
    
    strcpy(presets[2].name, "10MHz_0.3V");
    presets[2].frequency_hz = 10000000;
    presets[2].target_amplitude_mv = 300;
    presets[2].gain_factor = 1.0;

    /* 初始化AD9910 */
    AD9910_Init();

    /* 设置基准测试信号：1MHz，0.5V正弦波 */
    AD9910_SetFrequency(1000000);  // 1MHz
    AD9910_SetAmplitude(500);      // 0.5V峰峰值

    /* 关键：启用AD9910输出 */
    AD9910_EnableOutput();

    /* 更新系统参数 */
    system_params.frequency_hz = 1000000;
    system_params.amplitude_mv = 500;
    system_params.output_enabled = true;

    system_initialized = true;

    return CTRL_STATUS_OK;
}

/**
 * @brief  执行控制命令
 * @param  cmd: 控制命令
 * @param  param1: 参数1
 * @param  param2: 参数2
 * @retval ControlStatus_t 执行状态
 */
ControlStatus_t AD9910_Control_Execute(ControlCommand_t cmd, uint32_t param1, uint32_t param2)
{
    ControlStatus_t status = CTRL_STATUS_OK;
    
    if (!system_initialized) {
        return CTRL_STATUS_NOT_READY;
    }
    
    switch (cmd) {
        case CMD_SET_FREQUENCY:
            status = AD9910_Control_SetFrequency(param1);
            break;
            
        case CMD_SET_AMPLITUDE:
            status = AD9910_Control_SetTargetAmplitude((uint16_t)param1);
            break;
            
        case CMD_SET_GAIN:
            /* param1为增益系数的整数部分，param2为小数部分(x1000) */
            {
                double gain = (double)param1 + (double)param2 / 1000.0;
                status = AD9910_Control_SetGainFactor(gain);
            }
            break;
            
        case CMD_ENABLE_OUTPUT:
            status = AD9910_Control_EnableOutput(true);
            break;
            
        case CMD_DISABLE_OUTPUT:
            status = AD9910_Control_EnableOutput(false);
            break;
            
        case CMD_RESET:
            AD9910_SoftwareReset();
            status = AD9910_Control_Init();
            break;
            
        case CMD_SET_PRESET:
            status = AD9910_Control_LoadPreset((uint8_t)param1);
            break;
            
        default:
            status = CTRL_STATUS_INVALID_PARAM;
            break;
    }
    
    /* 调用回调函数 */
    if (control_callback != NULL) {
        control_callback(cmd, status, &system_params);
    }
    
    return status;
}

/**
 * @brief  设置频率
 * @param  frequency_hz: 频率值 (Hz)
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_SetFrequency(uint32_t frequency_hz)
{
    ControlStatus_t status;
    
    /* 参数验证 */
    status = ValidateFrequency(frequency_hz);
    if (status != CTRL_STATUS_OK) {
        return status;
    }
    
    /* 设置AD9910频率 */
    AD9910_SetFrequency_Precision(frequency_hz);
    
    /* 更新系统参数 */
    system_params.frequency_hz = frequency_hz;
    UpdateSystemTime();
    
    return CTRL_STATUS_OK;
}

/**
 * @brief  设置目标峰峰值 (考虑增益)
 * @param  target_amplitude_mv: 目标峰峰值 (mV)
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_SetTargetAmplitude(uint16_t target_amplitude_mv)
{
    ControlStatus_t status;
    uint16_t actual_output;
    
    /* 参数验证 */
    status = ValidateAmplitude(target_amplitude_mv);
    if (status != CTRL_STATUS_OK) {
        return status;
    }
    
    /* 计算AD9910实际输出值 */
    actual_output = AD9910_Control_CalculateActualOutput(target_amplitude_mv, system_params.gain_factor);
    
    /* 验证AD9910输出范围 */
    if (actual_output > AD9910_MAX_AMPLITUDE_MV) {
        return CTRL_STATUS_OUT_OF_RANGE;
    }
    
    /* 设置AD9910幅度 */
    AD9910_SetAmplitude_Precision(actual_output);
    
    /* 更新系统参数 */
    system_params.target_amplitude_mv = target_amplitude_mv;
    system_params.amplitude_mv = actual_output;
    UpdateSystemTime();
    
    return CTRL_STATUS_OK;
}

/**
 * @brief  设置增益系数
 * @param  gain_factor: 增益系数
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_SetGainFactor(double gain_factor)
{
    ControlStatus_t status;
    uint16_t actual_output;
    
    /* 参数验证 */
    status = ValidateGainFactor(gain_factor);
    if (status != CTRL_STATUS_OK) {
        return status;
    }
    
    /* 更新增益系数 */
    system_params.gain_factor = gain_factor;
    
    /* 重新计算并设置幅度 */
    actual_output = AD9910_Control_CalculateActualOutput(system_params.target_amplitude_mv, gain_factor);
    
    if (actual_output <= AD9910_MAX_AMPLITUDE_MV) {
        AD9910_SetAmplitude_Precision(actual_output);
        system_params.amplitude_mv = actual_output;
    } else {
        /* 如果超出范围，调整目标幅度 */
        system_params.amplitude_mv = AD9910_MAX_AMPLITUDE_MV;
        system_params.target_amplitude_mv = AD9910_Control_CalculateTargetOutput(AD9910_MAX_AMPLITUDE_MV, gain_factor);
        AD9910_SetAmplitude_Precision(AD9910_MAX_AMPLITUDE_MV);
    }
    
    UpdateSystemTime();
    
    return CTRL_STATUS_OK;
}

/**
 * @brief  使能/禁用输出
 * @param  enable: true-使能, false-禁用
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_EnableOutput(bool enable)
{
    if (enable) {
        AD9910_EnableOutput();
    } else {
        AD9910_DisableOutput();
    }
    
    system_params.output_enabled = enable;
    UpdateSystemTime();
    
    return CTRL_STATUS_OK;
}

/**
 * @brief  获取当前系统参数
 * @param  params: 参数结构体指针
 * @retval ControlStatus_t 获取状态
 */
ControlStatus_t AD9910_Control_GetParams(SystemParams_t* params)
{
    if (params == NULL) {
        return CTRL_STATUS_INVALID_PARAM;
    }
    
    *params = system_params;
    return CTRL_STATUS_OK;
}

/**
 * @brief  获取参数范围
 * @param  ranges: 参数范围结构体指针
 * @retval ControlStatus_t 获取状态
 */
ControlStatus_t AD9910_Control_GetRanges(ParamRanges_t* ranges)
{
    if (ranges == NULL) {
        return CTRL_STATUS_INVALID_PARAM;
    }
    
    *ranges = param_ranges;
    return CTRL_STATUS_OK;
}

/**
 * @brief  设置预设参数
 * @param  preset_id: 预设编号 (0-7)
 * @param  preset: 预设参数指针
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_SetPreset(uint8_t preset_id, const PresetParams_t* preset)
{
    if (preset_id >= CTRL_MAX_PRESETS || preset == NULL) {
        return CTRL_STATUS_INVALID_PARAM;
    }
    
    /* 验证预设参数 */
    if (!AD9910_Control_ValidateParams(preset->frequency_hz, preset->target_amplitude_mv, preset->gain_factor)) {
        return CTRL_STATUS_INVALID_PARAM;
    }
    
    presets[preset_id] = *preset;
    return CTRL_STATUS_OK;
}

/**
 * @brief  加载预设参数
 * @param  preset_id: 预设编号 (0-7)
 * @retval ControlStatus_t 加载状态
 */
ControlStatus_t AD9910_Control_LoadPreset(uint8_t preset_id)
{
    ControlStatus_t status;
    
    if (preset_id >= CTRL_MAX_PRESETS) {
        return CTRL_STATUS_INVALID_PARAM;
    }
    
    /* 检查预设是否有效 */
    if (presets[preset_id].frequency_hz == 0) {
        return CTRL_STATUS_INVALID_PARAM;
    }
    
    /* 设置增益系数 */
    status = AD9910_Control_SetGainFactor(presets[preset_id].gain_factor);
    if (status != CTRL_STATUS_OK) return status;
    
    /* 设置频率 */
    status = AD9910_Control_SetFrequency(presets[preset_id].frequency_hz);
    if (status != CTRL_STATUS_OK) return status;
    
    /* 设置目标幅度 */
    status = AD9910_Control_SetTargetAmplitude(presets[preset_id].target_amplitude_mv);
    if (status != CTRL_STATUS_OK) return status;
    
    return CTRL_STATUS_OK;
}

/**
 * @brief  注册控制回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void AD9910_Control_RegisterCallback(ControlCallback_t callback)
{
    control_callback = callback;
}

/**
 * @brief  控制系统任务处理 (在主循环中调用)
 * @param  None
 * @retval None
 */
void AD9910_Control_Task(void)
{
    if (!system_initialized) {
        return;
    }

    /* 强制更新AD9910参数 - 确保硬件同步 */
    AD9910_SetFrequency(system_params.frequency_hz);
    AD9910_SetAmplitude(system_params.amplitude_mv);

    /* 确保AD9910输出始终启用 */
    if (system_params.output_enabled) {
        AD9910_EnableOutput();
    } else {
        AD9910_DisableOutput();
    }

    /* 强制I/O更新，确保参数生效 */
    AD9910_HAL_IOUpdate();
}

/* ==================== 增益计算相关函数 ==================== */

/**
 * @brief  计算AD9910实际输出值 (考虑增益)
 * @param  target_amplitude_mv: 目标输出峰峰值
 * @param  gain_factor: 增益系数
 * @retval 计算得到的AD9910输出值
 */
uint16_t AD9910_Control_CalculateActualOutput(uint16_t target_amplitude_mv, double gain_factor)
{
    /* AD9910输出 = 目标输出 / 增益系数 */
    double actual_output = (double)target_amplitude_mv / gain_factor;
    
    /* 限制在有效范围内 */
    if (actual_output < 0) actual_output = 0;
    if (actual_output > AD9910_MAX_AMPLITUDE_MV) actual_output = AD9910_MAX_AMPLITUDE_MV;
    
    return (uint16_t)(actual_output + 0.5);  // 四舍五入
}

/**
 * @brief  计算目标输出值 (已知AD9910输出和增益)
 * @param  actual_output_mv: AD9910实际输出
 * @param  gain_factor: 增益系数
 * @retval 计算得到的目标输出值
 */
uint16_t AD9910_Control_CalculateTargetOutput(uint16_t actual_output_mv, double gain_factor)
{
    /* 目标输出 = AD9910输出 * 增益系数 */
    double target_output = (double)actual_output_mv * gain_factor;
    
    return (uint16_t)(target_output + 0.5);  // 四舍五入
}

/**
 * @brief  验证参数有效性
 * @param  frequency_hz: 频率值
 * @param  amplitude_mv: 峰峰值
 * @param  gain_factor: 增益系数
 * @retval true-有效, false-无效
 */
bool AD9910_Control_ValidateParams(uint32_t frequency_hz, uint16_t amplitude_mv, double gain_factor)
{
    return (ValidateFrequency(frequency_hz) == CTRL_STATUS_OK) &&
           (ValidateAmplitude(amplitude_mv) == CTRL_STATUS_OK) &&
           (ValidateGainFactor(gain_factor) == CTRL_STATUS_OK);
}

/* ==================== 私有函数实现 ==================== */

/**
 * @brief  更新系统时间戳
 * @param  None
 * @retval None
 */
static void UpdateSystemTime(void)
{
    system_params.last_update_time = SysTick_GetTick();
}

/**
 * @brief  验证频率参数
 * @param  frequency_hz: 频率值
 * @retval ControlStatus_t 验证结果
 */
static ControlStatus_t ValidateFrequency(uint32_t frequency_hz)
{
    if (frequency_hz < param_ranges.min_frequency_hz || frequency_hz > param_ranges.max_frequency_hz) {
        return CTRL_STATUS_OUT_OF_RANGE;
    }
    return CTRL_STATUS_OK;
}

/**
 * @brief  验证幅度参数
 * @param  amplitude_mv: 幅度值
 * @retval ControlStatus_t 验证结果
 */
static ControlStatus_t ValidateAmplitude(uint16_t amplitude_mv)
{
    if (amplitude_mv < param_ranges.min_amplitude_mv || amplitude_mv > param_ranges.max_amplitude_mv) {
        return CTRL_STATUS_OUT_OF_RANGE;
    }
    return CTRL_STATUS_OK;
}

/**
 * @brief  验证增益系数参数
 * @param  gain_factor: 增益系数
 * @retval ControlStatus_t 验证结果
 */
static ControlStatus_t ValidateGainFactor(double gain_factor)
{
    if (gain_factor < param_ranges.min_gain_factor || gain_factor > param_ranges.max_gain_factor) {
        return CTRL_STATUS_OUT_OF_RANGE;
    }
    return CTRL_STATUS_OK;
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
