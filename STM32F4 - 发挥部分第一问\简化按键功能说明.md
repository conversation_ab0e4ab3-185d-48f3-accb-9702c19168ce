# 简化按键功能说明

## 🎹 第一块矩阵键盘 - 直接步进控制

```
+-----+-----+-----+-----+
| 1k  |100k | 1M  |100Hz| 
+-----+-----+-----+-----+
|  4  |  5  |  6  |100Hz| 
+-----+-----+-----+-----+
|  7  |  8  |  9  | 0.1V| 
+-----+-----+-----+-----+
|  *  | 1V  |  #  | 0.1V| 
+-----+-----+-----+-----+
```

## 📋 按键功能表

| 按键 | 功能 | 说明 |
|------|------|------|
| **1** | +1kHz | 频率增加1kHz |
| **2** | +100kHz | 频率增加100kHz |
| **3** | +1MHz | 频率增加1MHz |
| **0** | +1V | 幅度增加1V |
| **F+** | +100Hz | 频率增加100Hz |
| **F-** | -100Hz | 频率减少100Hz |
| **A+** | +0.1V | 幅度增加0.1V |
| **A-** | -0.1V | 幅度减少0.1V |

## ⚡ 特点

- **按键直接生效**：无需确认，按下立即改变波形
- **实时更新**：参数改变后立即更新AD9910和显示
- **简单直观**：6种步进满足所有调节需求

## 🎯 使用示例

1. **快速调到5MHz**：按键3五次
2. **精确调节**：用100Hz步进微调
3. **设置幅度**：用1V大步进+0.1V精调
4. **频率扫描**：连续按1kHz或100kHz步进

## 📊 步进范围

- **频率范围**：1Hz - 100MHz
- **幅度范围**：0.1V - 5.0V
- **步进精度**：100Hz最小步进，1MHz最大步进
- **幅度精度**：0.1V最小步进，1V最大步进

**系统已简化完成，操作更加直观高效！**
